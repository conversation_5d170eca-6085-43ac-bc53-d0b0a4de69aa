<template>
  <div class="email-detail-container">
    <!-- 使用 EmailDetailContent 组件替换原有的邮件详情内容 -->
    <email-detail-content
      :email-detail="email"
      :detail-loading="detailLoading"
      :show-toolbar="true"
      :has-prev-email="false"
      :has-next-email="false"
      :compose-data="composeData"
      :languages="languages"
      :show-email-meta="showEmailMeta"
      :show-ai-summary="showAiSummary"
      :is-generating-summary="isGeneratingSummary"
      :ai-summary-content="aiSummaryContent"
      :customer-info="customerInfo"
      :show-all-to="showAllTo"
      :show-all-cc="showAllCc"
      @reply="handleReply"
      @reply-all="handleReplyAll"
      @forward="handleForward"
      @archive="handleArchive"
      @tag="handleTag"
      @distribute="handleDistribute"
      @translate="handleTranslate"
      @star="handleStar"
      @delete="handleDelete"
      @set-reminder="handleSetReminder"
      @navigate="handleNavigate"
      @fullscreen="handleFullscreen"
      @add-newclues="handleAddNewClues"
      @add-salesorder="handleAddSalesOrder"
      @edit-draft="handleEditDraft"
      @toggle-email-meta="toggleEmailMeta"
      @open-email-exchange="handleOpenEmailExchange"
      @remove-tag="handleRemoveTag"
      @toggle-to-expand="toggleToExpand"
      @toggle-cc-expand="toggleCcExpand"
      @close-ai-summary="closeAiSummary"
      @perform-translation="performTranslation"
      @toggle-original-view="toggleOriginalView"
      @close-translation="closeTranslation"
      @preview-attachment="handlePreviewAttachment"
      @download-attachment="handleDownloadAttachment"
    />
  </div>
</template>

<script>
import { formatTime } from '@/utils/index'
import { queryEmailDetailsAPI } from '@/api/crm/email'
import EmailDetailContent from './components/EmailDetailContent.vue'

export default {
  name: 'EmailDetail',
  components: {
    EmailDetailContent
  },
  data() {
    return {
      email: {},
      detailLoading: false,
      // 抄送人展开/收起状态
      showAllCc: false,
      // 收件人展开/收起状态
      showAllTo: false,
      // 邮件元信息显示状态
      showEmailMeta: false,
      // AI摘要相关
      showAiSummary: false,
      isGeneratingSummary: false,
      aiSummaryContent: '',
      // 客户信息
      customerInfo: {},
      // 写邮件数据
      composeData: {
        userName: '',
        from: ''
      },
      // 语言列表
      languages: [
        { code: 'zh', name: '中文' },
        { code: 'en', name: 'English' },
        { code: 'ja', name: '日本語' },
        { code: 'ko', name: '한국어' },
        { code: 'fr', name: 'Français' },
        { code: 'de', name: 'Deutsch' },
        { code: 'es', name: 'Español' },
        { code: 'it', name: 'Italiano' },
        { code: 'pt', name: 'Português' },
        { code: 'ru', name: 'Русский' }
      ]
    }
  },
  created() {
    this.loadEmailDetail()
  },
  watch: {
    // 监听路由变化，重新加载邮件详情
    '$route'(to, from) {
      if (to.params.id !== from.params.id) {
        this.loadEmailDetail()
      }
    }
  },
  methods: {
    loadEmailDetail() {
      const emailId = this.$route.params.id
      console.log('路由参数:', this.$route.params)
      if (emailId) {
        this.detailLoading = true
        this.fetchEmailDetailFromAPI(emailId)
      }
    },
    getRecipientUsernames(toList) {
      if (!toList || toList.length === 0) return '';

      const maxDisplay = 5;
      const usernames = toList.map(recipient => {
        const email = recipient.emailAddress || recipient;
        return email.split('@')[0];
      });

      if (usernames.length <= maxDisplay) {
        return usernames.join('，');
      } else {
        const displayNames = usernames.slice(0, maxDisplay).join('，');
        const remainingCount = usernames.length - maxDisplay;
        return `${displayNames}，等${remainingCount}人`;
      }
    },
    // 切换抄送人展开/收起状态
    toggleCcExpand() {
      this.showAllCc = !this.showAllCc;
    },

    // 切换收件人展开/收起状态
    toggleToExpand() {
      this.showAllTo = !this.showAllTo;
    },

    // 通过API获取邮件详情
    fetchEmailDetailFromAPI(emailId) {
      console.log('开始获取邮件详情:', emailId)

      queryEmailDetailsAPI({ id: emailId })
        .then(res => {
          console.log('邮件详情获取成功:', res.data)

          // 处理邮件详情数据，参考index.vue的字段映射
          const emailDetail = {
            ...res.data,
            // 字段映射：API字段 -> 页面期望字段
            sender: res.data.sendEmailAddress || '',
            sendEmailAddress: res.data.sendEmailAddress || '',
            time: res.data.sentTime || res.data.receivedTime,
            sendTime: res.data.sentTime || res.data.receivedTime,
            isStarred: res.data.isStarred || false,
            read: res.data.flagsSeen || false,
            flagsSeen: res.data.flagsSeen || false,
            subject: res.data.subject || '',
            content: res.data.content || '',
            receivedAddress: (res.data.toList && res.data.toList.length > 0) ? res.data.toList[0].emailAddress : '',
            toList: res.data.toList || [],
            ccList: res.data.ccList || [],
            bccList: res.data.bccList || [],
            attachments: res.data.fileList || [],
            fileList: res.data.fileList || [],
            hasAttachment: res.data.fileList && res.data.fileList.length > 0,
            size: res.data.size || 0,
            folder: res.data.folder || '',
            tags: res.data.tags || [],
            tagList: res.data.tagList || [],
            receivedDate: res.data.receivedTime || '',
            receivedTime: res.data.receivedTime || '',
            sendDate: res.data.sentTime || '',
            sentTime: res.data.sentTime || '',
            customerId: res.data.customerId || null,
            customerName: res.data.customerName || '',
            // 添加其他必要字段
            belongMailAccountAddress: res.data.belongMailAccountAddress || '',
            belongMailAccountUserName: res.data.belongMailAccountUserName || '',
            priority: res.data.priority || 1,
            trackingStatus: res.data.trackingStatus || 'sent'
          }

          // 设置邮件详情
          this.email = emailDetail
          console.log('邮件详情设置完成:', this.email)
        })
        .catch(error => {
          console.error('获取邮件详情失败:', error)
          this.$message.error('获取邮件详情失败')
        })
        .finally(() => {
          this.detailLoading = false
        })
    },

    handleReply(data) {
     const email = this.email;
      const type = data?.type || 'normal';

      console.log('回复邮件:', email, type);

      // 设置收件人为原邮件的发件人
      // 确保格式正确，以便 MultiEmailInput 组件能够正确处理
      // 如果有标签，使用 "Name <<EMAIL>>" 格式
      let to = '';
      if (email.status == 'inbox') {
        to = email.sendEmailAddress + (email.tag ? ` <${email.tag}>` : '');
      } else if (email.status == 'sent') {
        to = email.toList.map(item => item.emailAddress).join(",") + (email.tag ? ` <${email.tag}>` : '');
      }

      // 设置主题，添加"回复："前缀（如果没有的话）
      console.log("🔥 调试信息 - 详情页回复邮件原始subject:", email.subject);
      let subject = email.subject || '无主题';
      if (!subject.startsWith('回复:') && !subject.startsWith('Re:')) {
        subject = '回复: ' + subject;
      }
      console.log("🔥 调试信息 - 详情页回复邮件处理后subject:", subject);

      // 根据回复类型设置邮件内容
      let content;
      let attachments = [];
      if (type === 'without-original') {
        // 不带原文的回复
        content = '<p><br></p>';
      } else {
        // 普通回复或带附件回复，都包含原文
        const quoteHeader = `
          <p>------------------ 原始邮件 ------------------</p>
          <p>发件人: ${email.sendEmailAddress}${email.tag ? ` <${email.tag}>` : ''}</p>
          <p>发送时间: ${email.sentTime}</p>
          <p>主题: ${email.subject}</p>
          <p>收件人: ${email.toList ? email.toList.map(item => item.emailAddress).join(",") : ''}</p>
          <p>------------------------------------------</p>
        `;

        content = `<p><br></p><p><br></p>${quoteHeader}<blockquote style="padding-left: 10px; color: #666;">${email.content || ''}</blockquote>`;
      }

      if (type === 'with-attachment' && email.attachments && email.attachments.length > 0) {
        // 将附件信息序列化为JSON字符串传递
        attachments = encodeURIComponent(JSON.stringify(email.attachments));
        console.log('传递附件信息:', email.attachments);
      } else if (type === 'with-attachment' && email.fileList && email.fileList.length > 0) {
        // 兼容不同的附件字段名
        attachments = encodeURIComponent(JSON.stringify(email.fileList));
        console.log('传递附件信息(fileList):', email.fileList);
      }

      this.$router.push({
        name: 'EmailComposePage',
        params: {
          id: email.id,
          mode:'reply'
        },
        query: {
          from: this.composeData.from,
          to,
          subject: encodeURIComponent(subject),
          content: encodeURIComponent(content),
          replyMode: 'reply',
          originalEmailId: email.id,
          userId: this.composeData.userId,
          attachments
        }
      });
    },

     // 回复全部
    handleReplyAll(data) {
      // 兼容新旧格式
      const email = this.email;
      const type = data?.type || 'normal';

      // 设置收件人为原邮件的发件人
      let to = '';
      let cc = '';
      let bcc = '';
      // 确保格式正确，以便 MultiEmailInput 组件能够正确处理
      if (email.status == 'inbox') {
        to = email.sendEmailAddress + ',' + (email.toList.map(item => item.emailAddress).join(',') || '')
      } else if (email.status == 'sent') {
        to = email.sendEmailAddress + ',' + (email.toList.map(item => item.emailAddress).join(',') || '')
      }

      if (email.ccList && email.ccList.length > 0) {
        email.ccList.map(item => {
          cc = item.emailAddress + ',';
        });
      }

      if (email.bccList && email.bccList.length > 0) {
        // 如果有密送人，添加到密送列表
        email.bccList.map(item => {
          bccList = item.emailAddress + ',';
        });
      }

      // 设置主题，添加"回复："前缀（如果没有的话）
      let subject = email.subject;
      if (!subject.startsWith('回复:') && !subject.startsWith('Re:')) {
        subject = '回复: ' + subject;
      }

      // 根据回复类型设置邮件内容
      let content;
      if (type === 'without-original') {
        // 不带原文的回复全部
        content = '<p><br></p>';
      } else {
        // 普通回复全部或带附件回复全部，都包含原文
        const quoteHeader = `
          <p>------------------ 原始邮件 ------------------</p>
          <p>发件人: ${email.sendEmailAddress}${email.tag ? ` <${email.tag}>` : ''}</p>
          <p>发送时间: ${email.sentTime}</p>
          <p>主题: ${email.subject}</p>
          <p>收件人: ${email.toList ? email.toList.map(item => item.emailAddress).join(",") : ''}</p>
          <p>------------------------------------------</p>
        `;

        content = `<p><br></p><p><br></p>${quoteHeader}<blockquote style="padding-left: 10px; color: #666;">${email.content || ''}</blockquote>`;
      }

      // 🔥 新增：处理附件信息
      let queryParams = {
        from: this.composeData.from,
        to: to,
        cc: cc,
        bcc: bcc,
        subject: encodeURIComponent(subject),
        content: encodeURIComponent(content),
        replyMode: 'replyAll',
        originalEmailId: email.id,
        userId: this.composeData.userId
      };

      // 如果是带附件的回复全部，传递附件信息
      if (type === 'with-attachment' && email.attachments && email.attachments.length > 0) {
        // 将附件信息序列化为JSON字符串传递
        queryParams.attachments = encodeURIComponent(JSON.stringify(email.attachments));
        console.log('传递附件信息:', email.attachments);
      } else if (type === 'with-attachment' && email.fileList && email.fileList.length > 0) {
        // 兼容不同的附件字段名
        queryParams.attachments = encodeURIComponent(JSON.stringify(email.fileList));
        console.log('传递附件信息(fileList):', email.fileList);
      }

      // 通过路由跳转到写邮件页面
      this.$router.push({
        name: 'EmailComposePage',
        params: {
          id: email.id,
          mode:'replyAll'
        },
        query: queryParams
      });
    },

    handleForward(data) {
      const email = this.email;
      const type = data?.type || 'normal';


      // 设置主题，添加"转发："前缀（如果没有的话）
      console.log("🔥 调试信息 - 详情页转发邮件原始subject:", email.subject);
      let subject = email.subject || '无主题';
      if (!subject.startsWith('转发:') && !subject.startsWith('Fwd:')) {
        subject = '转发: ' + subject;
      }
      console.log("🔥 调试信息 - 详情页转发邮件处理后subject:", subject);

      // 根据转发类型设置邮件内容
      let content;
      let attachments = [];

      if (type === 'without-original') {
        // 不带原文的转发
        content = '<p><br></p>';
      } else {
        // 普通转发，包含原文
        const forwardHeader = `
          <p>------------------ 转发邮件 ------------------</p>
          <p>发件人: ${email.sendEmailAddress}${email.tag ? ` <${email.tag}>` : ''}</p>
          <p>发送时间: ${email.sentTime}</p>
          <p>主题: ${email.subject}</p>
          <p>收件人: ${email.toList ? email.toList.map(item => item.emailAddress).join(",") : ''}</p>
          <p>------------------------------------------</p>
        `;

        content = `<p><br></p><p><br></p>${forwardHeader}<div>${email.content || ''}</div>`;

        // 如果有签名，也包含在转发内容中
        if (email.signature) {
          content += `<div>${email.signature}</div>`;
        }
      }

      // 复制附件（普通转发时包含附件）
      if (email.attachments && email.attachments.length > 0) {
        attachments = encodeURIComponent(JSON.stringify(email.attachments));
      } else if (email.fileList && email.fileList.length > 0) {
        // 兼容不同的附件字段名
        attachments = encodeURIComponent(JSON.stringify(email.fileList));
        console.log('传递附件信息(fileList):', email.fileList);
      }

      // 通过路由跳转到写邮件页面
      this.$router.push({
        name: 'EmailComposePage',
        params: {
          id: email.id,
          mode:'forward'
        },
        query: {
          from: this.composeData.from,
          to:'',
          subject: encodeURIComponent(subject),
          content: encodeURIComponent(content),
          replyMode: 'forward',
          originalEmailId: email.id,
          userId: this.composeData.userId,
          attachments
        }
      });
    },

    handleDownloadAttachment(attachment) {
      // 下载附件
      console.log('下载附件:', attachment)
    },

    handleArchive() {
      // 处理归档
      console.log('归档邮件:', this.email)
    },

    handleTag() {
      // 处理标签
      console.log('标签邮件:', this.email)
    },

    handleDistribute() {
      // 处理分发
      console.log('分发邮件:', this.email)
    },

    handleTranslate() {
      // 处理翻译
      console.log('翻译邮件:', this.email)
    },

    handleStar() {
      // 处理星标
      console.log('星标邮件:', this.email)
    },

    handleDelete() {
      // 处理删除
      console.log('删除邮件:', this.email)
    },

    handleSetReminder(data) {
      // 处理设置提醒
      console.log('设置提醒:', data)
    },

    handleNavigate(direction) {
      // 处理导航
      console.log('导航:', direction)
    },

    handleFullscreen() {
      // 处理全屏
      console.log('全屏显示:', this.email)
    },

    handleAddNewClues() {
      // 处理添加新线索
      console.log('添加新线索:', this.email)
    },

    handleAddSalesOrder() {
      // 处理添加销售订单
      console.log('添加销售订单:', this.email)
    },

    handleEditDraft() {
      // 处理编辑草稿
      console.log('编辑草稿:', this.email)
    },

    toggleEmailMeta() {
      // 切换邮件元信息显示
      this.showEmailMeta = !this.showEmailMeta
    },

    handleOpenEmailExchange() {
      // 处理打开邮件往来
      console.log('打开邮件往来:', this.email)
    },

    handleRemoveTag(data) {
      // 处理移除标签
      console.log('移除标签:', data)
    },

    closeAiSummary() {
      // 关闭AI摘要
      this.showAiSummary = false
    },

    performTranslation() {
      // 执行翻译
      console.log('执行翻译')
    },

    toggleOriginalView() {
      // 切换原文显示
      console.log('切换原文显示')
    },

    closeTranslation() {
      // 关闭翻译
      console.log('关闭翻译')
    },

    handlePreviewAttachment(attachment) {
      // 预览附件
      console.log('预览附件:', attachment)
    },

    formatTime(time) {
      return formatTime(time)
    },
    getSendStatusText(trackingStatus) {
      switch (trackingStatus) {
        case 'sent':
          return '已发送';
        case 'delivered':
          return '已送达';
        case 'opened':
          return '已打开';
        default:
          return '已发送';
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.email-detail-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
}
</style>